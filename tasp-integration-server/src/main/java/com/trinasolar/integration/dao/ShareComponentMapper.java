package com.trinasolar.integration.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.integration.api.entity.ShareComponentDO;
import com.trinasolar.integration.controller.component.ShareComponentReq;
import com.trinasolar.integration.controller.component.ShareComponentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ShareComponentMapper extends BaseMapper<ShareComponentDO> {
    Page<ShareComponentVO> shareComponentPage(Page<ShareComponentVO> page, ShareComponentReq req);

    int checkComponentNameExists(@Param("componentName") String componentName,@Param("id") Long id);

    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    int deleteComponentById(@Param("id")Long id);

    int checkUpdateComponentNameExists(@Param("componentName") String componentName,@Param("id") Long id);
}
