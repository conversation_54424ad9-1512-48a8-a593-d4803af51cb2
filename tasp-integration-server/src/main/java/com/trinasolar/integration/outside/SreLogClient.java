package com.trinasolar.integration.outside;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.trinasolar.integration.dto.appLogs.SreLogPageDTO;
import com.trinasolar.integration.dto.appLogs.SreLogQueryDTO;
import com.trinasolar.integration.dto.appLogs.SreLogResultDTO;
import com.trinasolar.integration.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日志易API请求客户端
 */
@Component
@Slf4j
public class SreLogClient {

    @Value("${sreLog.username}")
    private String username;

    @Value("${sreLog.password}")
    private String password;

    @Value("${sreLog.domain}")
    private String domain;

    @Value("${sreLog.searchSubmitUrl}")
    private String searchSubmitUrl;

    @Value("${sreLog.searchFetchUrl}")
    private String searchFetchUrl;

    @Value("${sreLog.jumpUrl}")
    private String jumpUrl;

    @Value("${sreLog.downloadSubmitUrl}")
    private String downloadSubmitUrl;

    @Value("${sreLog.downloadFetchUrl}")
    private String downloadFetchUrl;

    @Value("${spring.profiles.active}")
    private String env;


    /**
     * 调用日志易-搜索任务提交接口
     * @return
     */
    public SreLogResultDTO searchSubmit(SreLogQueryDTO queryDTO) {
        try{
            String url = domain + searchSubmitUrl;
            log.info("调用日志易-搜索任务提交接口-入参：{},URL:{}", JSONObject.toJSON(queryDTO), url);
            // 设置参数
            Map<String,Object> param = new HashMap<>();
            String queryUrl = this.builderSearchParams(param, queryDTO);
            Map<String,String> header = new HashMap<>();
            header.put("Content-Type", "application/json");
            header.put("Authorization", "Basic " + Base64.encode(username + ":" + password));
            String resultStr = HttpClientUtil.doGet(url + queryUrl, null, header);
            if (Objects.nonNull(resultStr)) {
                log.info("调用日志易-搜索任务提交接口返回结果：{}", JSONObject.toJSON(resultStr));
                SreLogResultDTO resultDTO = JSONObject.parseObject(resultStr, new TypeReference<SreLogResultDTO>() {});
                if (!resultDTO.isResult()) {
                    log.error("调用日志易-搜索任务提交接口失败，返回结果为：{}", JSONObject.toJSON(resultStr));
                }else {
                    return resultDTO;
                }
            }
            return null;
        }catch (Exception e) {
            log.error("调用日志易-搜索任务提交接口报错:", e);
            return null;
        }
    }

    /**
     * 调用日志易-获取搜索任务提交接口
     * @return
     */
    public SreLogPageDTO searchFetch(SreLogQueryDTO queryDTO, SreLogResultDTO submitLogResult) {
        try{
            String url = domain + searchFetchUrl;
            log.info("调用日志易-获取搜索任务提交接口-入参：{},URL:{}", JSONObject.toJSON(queryDTO), url);
            // 设置参数
            Map<String,Object> param = new HashMap<>();
            String queryUrl = this.builderFetchParams(param, queryDTO, submitLogResult);
            Map<String,String> header = new HashMap<>();
            log.info("调用日志易-获取搜索任务提交接口-URL：{}", url + queryUrl);
            header.put("Content-Type", "application/json");
            header.put("Authorization", "Basic " + Base64.encode(username + ":" + password));
            String resultStr = HttpClientUtil.doGet(url + queryUrl, null, header);
            if (Objects.nonNull(resultStr)) {
                log.info("调用日志易-获取搜索任务提交接口返回结果：{}", JSONObject.toJSON(resultStr));
                SreLogResultDTO<String> resultDTO = JSONObject.parseObject(resultStr, new TypeReference<SreLogResultDTO<String>>() {});
                if (!resultDTO.isResult()) {
                    log.error("调用日志易-获取搜索任务提交接口失败，返回结果为：{}", JSONObject.toJSON(resultStr));
                }else {
                    SreLogPageDTO logs = new SreLogPageDTO();
                    if (Objects.nonNull(resultDTO.getResults())) {
                        // 解析接口返回的JSON对象
                        logs.setHasData(this.doParseResult(resultDTO.getResults(), logs, queryDTO));
                    }else {
                        logs.setRecords(new ArrayList<>());
                    }
                    return logs;
                }
            }
            return null;
        }catch (Exception e) {
            log.error("调用日志易-获取搜索任务提交接口报错:", e);
            return null;
        }
    }

    /**
     * 调用日志易-下载任务提交接口
     * @return
     */
    public SreLogResultDTO downloadSubmit(SreLogQueryDTO queryDTO) {
        try{
            String url = domain + downloadSubmitUrl;
            log.info("调用日志易-下载任务提交接口-入参：{},URL:{}", JSONObject.toJSON(queryDTO), url);
            // 设置参数
            Map<String,Object> param = new HashMap<>();
            String queryUrl = this.builderDownloadSearchParams(param, queryDTO);
            Map<String,String> header = new HashMap<>();
            header.put("Content-Type", "application/json");
            header.put("Authorization", "Basic " + Base64.encode(username + ":" + password));
            String resultStr = HttpClientUtil.doGet(url + queryUrl, null, header);
            if (Objects.nonNull(resultStr)) {
                SreLogResultDTO resultDTO = JSONObject.parseObject(resultStr, new TypeReference<SreLogResultDTO>() {});
                if (!resultDTO.isResult()) {
                    log.error("调用日志易-下载任务提交接口失败，返回结果为：{}", JSONObject.toJSON(resultStr));
                }else {
                    return resultDTO;
                }
            }
            return null;
        }catch (Exception e) {
            log.error("调用日志易-下载任务提交接口报错:", e);
            return null;
        }
    }


    /**
     * 调用日志易-获取下载任务提交接口
     * @return
     */
    public SreLogPageDTO downloadFetch(SreLogQueryDTO queryDTO, SreLogResultDTO submitLogResult) {
        try{
            String url = domain + downloadFetchUrl;
            log.info("调用日志易-获取下载任务提交接口-入参：{},URL:{}", JSONObject.toJSON(queryDTO), url);
            // 设置参数
            Map<String,Object> param = new HashMap<>();
            param.put("sid", submitLogResult.getSid());
            Map<String,String> header = new HashMap<>();
            header.put("Content-Type", "application/json");
            header.put("Authorization", "Basic " + Base64.encode(username + ":" + password));
            String resultStr = HttpClientUtil.doGet(url, param, header);
            if (Objects.nonNull(resultStr)) {
                SreLogResultDTO<String> resultDTO = JSONObject.parseObject(resultStr, new TypeReference<SreLogResultDTO<String>>() {});
                if (!resultDTO.isResult()) {
                    log.error("调用日志易-获取下载任务提交接口失败，返回结果为：{}", JSONObject.toJSON(resultStr));
                }else {
                    SreLogPageDTO logs = new SreLogPageDTO();
                    if (Objects.nonNull(resultDTO.getResults())) {
                        // 解析接口返回的JSON对象
                        this.doParseResult(resultDTO.getResults(), logs, queryDTO);
                    }
                    return logs;
                }
            }
            return null;
        }catch (Exception e) {
            log.error("调用日志易-获取下载任务提交接口报错:", e);
            return null;
        }
    }

    private boolean doParseResult(String results, SreLogPageDTO logs, SreLogQueryDTO queryDTO) {
        // 数据返回标志位
        boolean hasData = false;
        // 解析接口返回的JSON对象
        JSONObject resultsJson = JSONObject.parseObject(results);
        // 设置分页参数
        logs.setPage(queryDTO.getPage());
        logs.setSize(queryDTO.getSize());
        logs.setCurrent(queryDTO.getCurrent());
        // 解析结果数据
        if (resultsJson != null) {
            // 获取跳转url
            String jumpUri = jumpUrl + builderJumpUrlParams(queryDTO);
            JSONObject sheetsJson = resultsJson.getJSONObject("sheets");
            if (sheetsJson != null) {
                // 获取总记录数
                int totalCount = sheetsJson.getIntValue("total");
                logs.setTotal(totalCount);
                // 解析日志记录
                JSONArray rowsArray = sheetsJson.getJSONArray("rows");
                if (Objects.nonNull(rowsArray) && !rowsArray.isEmpty()) {
                    hasData = true;
                    List<JSONObject> records = new ArrayList<>(rowsArray.size());
                    for (Object rowObj : rowsArray) {
                        JSONObject rowJson = (JSONObject) rowObj;
                        if (Objects.nonNull(rowJson)) {
                            rowJson.putIfAbsent("jump_url", jumpUri);
                            Long timestamp = rowJson.getLong("timestamp");
                            if (Objects.nonNull(timestamp)) {
                                // 将时间戳转为时间字符串
                                String timeStr = DateUtil.format(new Date(timestamp), "yyyy-MM-dd HH:mm:ss");
                                rowJson.put("timestamp", timeStr);
                            }
                            records.add(rowJson);
                        }
                    }
                    logs.setRecords(records);
                }else {
                    logs.setRecords(new ArrayList<>());
                }
            }
        }
        return hasData;
    }

    private String builderFetchParams(Map<String, Object> param, SreLogQueryDTO queryDTO, SreLogResultDTO resultDTO) {
        // 添加所有必要参数
        param.put("sid", resultDTO.getSid());
        param.put("traceid", resultDTO.getTraceId());
        // 封装时间戳
        this.builderTimeRange(param, queryDTO);
        // 封装通用参数
        this.commonParams(param, queryDTO);
        param.put("category", "sheets");
        return "?" + param.entrySet().stream().map(entry -> entry.getKey() + "=" + entry.getValue()).collect(Collectors.joining("&"));

    }

    private void commonParams(Map<String, Object> param, SreLogQueryDTO queryDTO) {
        param.put("page", queryDTO.getPage() == null ? 0 : queryDTO.getPage() - 1);
        param.put("size", queryDTO.getSize() == null ? 20 : queryDTO.getSize());
        param.put("timeline", "true");
        param.put("statsevents", "true");
        param.put("category", "search");
        param.put("fromSearch", "true");
        param.put("order", "desc");
        param.put("datasets", "[]");
        param.put("filters", "");
        param.put("now", "");
        param.put("test_mode", "false");
        param.put("fields", "true");
        param.put("app_id", "");
        param.put("terminated_after_size", "");
        param.put("searchMode", "index");
        param.put("onlyTransactionDate", "false");
        param.put("highlight", "true");
        param.put("onlySortByTimestamp", "false");
        param.put("parameters", "%7B%7D");
        param.put("use_spark", "false");
        param.put("_t", String.valueOf(System.currentTimeMillis()));
        param.put("version", "-1");
        param.put("lang", "zh_CN");
        param.put("should_trace", "true");
        param.put("parent_spanid", "");
    }

    private String builderDownloadSearchParams(Map<String, Object> param, SreLogQueryDTO queryDTO) {
        // 添加所有必要参数
        // 封装query
        StringBuilder query = new StringBuilder();
        if (StringUtils.isNotEmpty(queryDTO.getKeyword())){
            query.append(queryDTO.getKeyword());
        }
        if (StringUtils.isNotEmpty(queryDTO.getSysName())){
            query.append("+AND+").append("sysname:").append(queryDTO.getSysName());
        }
        if (StringUtils.isNotEmpty(queryDTO.getAppName())){
            query.append("+AND+").append("service_name:").append(queryDTO.getAppName());
        }
        param.put("query", query);
        // 封装时间戳
        this.builderTimeRange(param, queryDTO);
        // 封装通用参数
        this.commonParams(param, queryDTO);
        param.put("file_format", "txt");
        param.put("charset_name", "UTF-8");
        param.put("file_name", System.currentTimeMillis());
        return "?" + param.entrySet().stream().map(entry -> entry.getKey() + "=" + entry.getValue()).collect(Collectors.joining("&"));
    }

    private String builderSearchParams(Map<String, Object> param, SreLogQueryDTO queryDTO) {
        // 添加所有必要参数
        // 封装query
        StringBuilder query = new StringBuilder();
        if (StringUtils.isNotEmpty(queryDTO.getKeyword())){
            query.append(queryDTO.getKeyword());
        }else {
            query.append("*");
        }
        if (StringUtils.isNotEmpty(env)){
            query.append("+AND+").append("env:").append(env);
        }
        if (StringUtils.isNotEmpty(queryDTO.getNamespaceSysName())){
            query.append("+AND+").append("sysname:").append(queryDTO.getNamespaceSysName()).append("*");
        }else {
            if (StringUtils.isNotEmpty(queryDTO.getSysName())){
                query.append("+AND+").append("sysname:*").append(queryDTO.getSysName()).append("*");
            }
        }
        if (StringUtils.isNotEmpty(queryDTO.getAppName())){
            query.append("+AND+").append("service_name:").append(queryDTO.getAppName());
        }
        param.put("timeline", "true");
        param.put("statsevents", "true");
        param.put("category", "search");
        // 封装时间戳
        this.builderTimeRange(param, queryDTO);
        param.put("fromSearch", "true");
        param.put("page", 0);
        param.put("size", 20);
        param.put("order", "desc");
        param.put("datasets", "[]");
        param.put("filters", "");
        param.put("now", "");
        param.put("test_mode", "false");
        param.put("fields", "true");
        param.put("app_id", "");
        param.put("terminated_after_size", "");
        param.put("searchMode", "index");
        param.put("onlyTransactionDate", "false");
        param.put("highlight", "true");
        param.put("onlySortByTimestamp", "false");
        param.put("parameters", "%7B%7D");
        param.put("use_spark", "false");
        param.put("_t", System.currentTimeMillis());
        param.put("version", "-1");
        param.put("query", query);
        param.put("lang", "zh_CN");
        param.put("should_trace", "true");
        param.put("parent_spanid", "");
        return "?" + param.entrySet().stream().map(entry -> entry.getKey() + "=" + entry.getValue()).collect(Collectors.joining("&"));
    }

    private void builderTimeRange(Map<String, Object> param, SreLogQueryDTO queryDTO) {
        // 方式一：先尝试解析时间区间
        if (StringUtils.isNotEmpty(queryDTO.getStartTime()) && StringUtils.isNotEmpty(queryDTO.getEndTime())){
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
                Date startDate = sdf.parse(queryDTO.getStartTime());
                Date endDate = sdf.parse(queryDTO.getEndTime());
                param.put("time_range", startDate.getTime() + "," + endDate.getTime());
                System.out.println(startDate.getTime() + "," + endDate.getTime());
            } catch (Exception e) {
                log.warn("时间区间解析失败，使用默认时间区间");
                param.put("time_range", System.currentTimeMillis() - 600000 + "," + System.currentTimeMillis());
            }
        }else {
            // 方式二：时间区间为空，则默认为当前时间10分钟的时间戳区间
            if (StringUtils.isEmpty(queryDTO.getTime()) || Objects.equals(queryDTO.getTime(), "1")){
                // 当前时间10分钟内的时间戳区间
                param.put("time_range", System.currentTimeMillis() - 600000 + "," + System.currentTimeMillis());
            }else if (Objects.equals(queryDTO.getTime(), "2")) {
                // 当前时间半小时内的时间戳区间
                param.put("time_range", System.currentTimeMillis() - 1800000 + "," + System.currentTimeMillis());
            } else if (Objects.equals(queryDTO.getTime(), "3")) {
                // 当前时间一天内的时间戳区间
                param.put("time_range", System.currentTimeMillis() - 86400000 + "," + System.currentTimeMillis());
            } else if (Objects.equals(queryDTO.getTime(), "4")) {
                // 当前时间一个月内的时间戳区间
                param.put("time_range", System.currentTimeMillis() - 2592000000L + "," + System.currentTimeMillis());
            } else if (Objects.equals(queryDTO.getTime(), "5")) {
                // 当前时间全部内的时间戳区间(1735664400000为：项目开始时间)
                param.put("time_range", "1735664400000," + System.currentTimeMillis());
            }
        }

    }

    private String builderJumpUrlParams(SreLogQueryDTO queryDTO) {
        Map<String,Object> param = new HashMap<>();
        // 添加所有必要参数
        // 封装query
        StringBuilder query = new StringBuilder();
        if (StringUtils.isNotEmpty(queryDTO.getKeyword())){
            query.append(queryDTO.getKeyword());
        }else {
            query.append("*");
        }
        if (StringUtils.isNotEmpty(env)){
            query.append("%20AND%20").append("env:").append(env);
        }
        if (StringUtils.isNotEmpty(queryDTO.getNamespaceSysName())){
            query.append("%20AND%20").append("sysname:").append(queryDTO.getNamespaceSysName()).append("*");
        }else {
            if (StringUtils.isNotEmpty(queryDTO.getSysName())){
                query.append("%20AND%20").append("sysname:*").append(queryDTO.getSysName()).append("*");
            }
        }
        if (StringUtils.isNotEmpty(queryDTO.getAppName())){
            query.append("%20AND%20").append("service_name:").append(queryDTO.getAppName());
        }
        param.put("timeline", "true");
        param.put("statsevents", "true");
        // 封装时间戳
        this.builderTimeRange(param, queryDTO);
        param.put("fromSearch", "true");
        param.put("page", queryDTO.getPage() == null ? 0 : queryDTO.getPage());
        param.put("size", queryDTO.getSize() == null ? 20 : queryDTO.getSize());
        param.put("order", "desc");
        param.put("datasets", "[]");
        param.put("filters", "");
        param.put("now", "");
        param.put("test_mode", "false");
        param.put("fields", "true");
        param.put("app_id", "");
        param.put("terminated_after_size", "");
        param.put("searchMode", "index");
        param.put("onlyTransactionDate", "false");
        param.put("highlight", "true");
        param.put("onlySortByTimestamp", "false");
        param.put("parameters", "");
        param.put("use_spark", "false");
        param.put("_t", System.currentTimeMillis());
        param.put("query", query);
        param.put("preview", "");
        return "?" + param.entrySet().stream().map(entry -> entry.getKey() + "=" + entry.getValue()).collect(Collectors.joining("&"));
    }

}
