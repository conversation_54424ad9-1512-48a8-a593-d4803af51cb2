package com.trinasolar.integration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.integration.api.RemoteDictProvider;
import com.trinasolar.integration.api.dto.R;
import com.trinasolar.integration.api.entity.*;
import com.trinasolar.integration.api.mapper.ApplicationProgramChangeMapper;
import com.trinasolar.integration.api.mapper.InteAppSystemChangeMapper;
import com.trinasolar.integration.dao.AppSyncLogMapper;
import com.trinasolar.integration.dao.AppSystemMapper;
import com.trinasolar.integration.dao.ApplicationProgramMapper;
import com.trinasolar.integration.service.DataShareService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.trinasolar.integration.kafka.core.KafkaProducerClient;
import org.springframework.kafka.core.KafkaAdmin;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.TimeUnit;

import com.trinasolar.tasc.framework.common.util.json.JsonUtils;
import cn.hutool.core.util.StrUtil;

/**
 * @className: DataShareServiceImpl
 * @Description: 数据共享服务实现类
 * @author: pengshy
 * @date: 2025/9/2 16:59
 */
@Slf4j
@Service
public class DataShareServiceImpl implements DataShareService {

    @Autowired
    private InteAppSystemChangeMapper appSystemChangeMapper;

    @Autowired
    private ApplicationProgramChangeMapper appProgramChangeMapper;

    @Autowired
    private AppSystemMapper appSystemMapper;

    @Autowired
    private ApplicationProgramMapper appProgramMapper;

    @Autowired
    private AppSyncLogMapper appSyncLogMapper;

    @Autowired
    private RemoteDictProvider dictClient;

    @Autowired(required = false)
    private KafkaProducerClient kafkaProducerClient;

    @Value("#{'${trinasolar.kafka.topic-exclusive:gitlab,devops,sca,apm}'.split(',')}")
    private List<String> dataShareTopicExclusive;

    @Autowired(required = false)
    private KafkaAdmin kafkaAdmin;

    @Value("${spring.kafka.bootstrap-servers:}")
    private String kafkaBootstrapServers;

    @Value("${trinasolar.kafka.partitions:3}")
    private int kafkaTopicPartitions;

    @Value("${trinasolar.kafka.replicas:1}")
    private short kafkaTopicReplicas;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final String SYSTEM_BIZ_DATA_FORMATE = "{\"flow_domain\":\"7.0\",\"app_domain\":\"mhr\",\"app_group\":\"osesat\",\"app_name\":\"osesa\",\"code\":\"MHR-ALL-HRP\",\"cn_name\":\"人力资源门户\",\"cn_simple_name\":\"人力资源\",\"product_line\":\"efap\",\"en_name\":\"HR portal\",\"en_simple_name\":\"HRP\",\"biz_unit\":\"gen\",\"biz_scope\":\"group\",\"description\":\"HR人力资源共享门户面向SAL/DL/IDL的自助服务平台/通过数字化服务/提升用户体验\",\"stage_status\":\"Online\",\"actually_time\":\"2025-1-16\",\"remark\":\"\",\"bus_user_name\":\"倪庆英\",\"requester_contact\":\"王宏智\",\"digital_owner\":\"薛波\",\"dev_user_name\":\"于庆伟\",\"om_user_name\":\"薛波\",\"external_product_name\":\"布谷\",\"construction_type\":\"Procurement\",\"version\":\"HZERO1.10\",\"product_vendor\":\"汉得\",\"impl_vendor\":\"汉得\",\"license_mode\":\"auth_perm\",\"app_service_level\":2,\"service_hours\":\"5*8\",\"biz_scope_type\":\"other\",\"dev_language\":\"lang_java\",\"business_domain\":\"mhr\",\"app_sys_type\":2,\"bus_user_id\":\"014438\",\"sys_user_id\":\"\",\"dev_user_id\":\"137863\",\"om_user_id\":\"282605\",\"requester_contact_id\":\"314958\",\"digital_owner_id\":\"282605\",\"admin\":\"\",\"admin_name\":\"\",\"sys_user_name\":\"\",\"access_url\":\"\",\"manage_url\":\"\",\"manual_url\":\"\",\"license_desc\":\"\",\"data_version\":\"\"}";

    // 解析模板字段，用于过滤业务数据
    private static final Set<String> SYSTEM_BIZ_DATA_FIELDS;

    static {
        Set<String> fields = new HashSet<>();
        try {
            Map<String, Object> template = JsonUtils.parseObject(SYSTEM_BIZ_DATA_FORMATE, Map.class);
            if (template != null) {
                fields.addAll(template.keySet());
            }
        } catch (Exception e) {
            log.error("解析SYSTEM_BIZ_DATA_FORMATE模板失败", e);
        }
        SYSTEM_BIZ_DATA_FIELDS = Collections.unmodifiableSet(fields);
    }

    @Override
    public void executeDataShareIncrement() {
        //查询应用系统变更记录
        List<InteAppSystemChange> inteAppSystemChanges = appSystemChangeMapper.selectList(
                new LambdaQueryWrapper<InteAppSystemChange>().eq(InteAppSystemChange::getIsProcessed, 0));
        //按照以下json格式填充共享数据
        //推送至kafka

        if (inteAppSystemChanges == null || inteAppSystemChanges.isEmpty()) {
            log.info("无待共享的应用系统变更数据");
            return;
        }

        if (!kafkaAvailable()) {
            return;
        }

        List<String> targetSystemTopics = buildTargetSystemTopics();

        for (InteAppSystemChange change : inteAppSystemChanges) {
            try {
                Map<String, Object> payload = buildAppSystemPayload(change);
                // 发送到各系统的专属 topic（按 topic 前缀设置 target_system）
                sendToTargetTopics(targetSystemTopics, change.getSimpleEnName(), payload);

                // 标记为已处理
                change.setIsProcessed(1);
                appSystemChangeMapper.updateById(change);
                log.info("已推送应用系统共享数据，systemCode={}, version={}", change.getSimpleEnName(), change.getCurrentVersion());
            } catch (Exception ex) {
                log.error("推送应用系统共享数据失败，systemCode={}, version={}", change.getSimpleEnName(), change.getCurrentVersion(), ex);
            }
        }

        log.info("数据共享任务执行完成");
    }

    @Override
    public void executeDataShareFull() {
        List<AppSystem> appSystems = appSystemMapper.selectList(new LambdaQueryWrapper<>());

        if (appSystems == null || appSystems.isEmpty()) {
            log.info("无待共享的应用系统全量数据");
            return;
        }

        if (!kafkaAvailable()) {
            return;
        }

        // 计算目标系统的专属 topic 列表并确保存在
        List<String> targetSystemTopics = buildTargetSystemTopics();

        String batchId = null;
        for (AppSystem appSystem : appSystems) {
            try {
                if (batchId == null) {
                    batchId = DATE_TIME_FORMATTER.format(java.time.LocalDateTime.now());
                }
                Map<String, Object> payload = buildAppSystemPayload(appSystem, batchId);
                // 发送到各系统的专属 topic（按 topic 前缀设置 target_system）
                sendToTargetTopics(targetSystemTopics, appSystem.getCode(), payload);
            } catch (Exception ex) {
                log.error("推送应用系统共享全量数据失败，systemCode={}", appSystem.getCode(), ex);
            }
        }
    }


    @Override
    public void executeDataShareProgramIncrement() {
        List<ApplicationProgramChange> applicationProgramChanges = appProgramChangeMapper.selectList(
                new LambdaQueryWrapper<ApplicationProgramChange>()
                        .eq(ApplicationProgramChange::getIsProcessed, 0));
        if (applicationProgramChanges == null || applicationProgramChanges.isEmpty()) {
            log.info("无待共享的应用程序增量数据");
            return;
        }

        if (!kafkaAvailable()) {
            return;
        }

        List<String> targetProgramTopics = buildTargetProgramTopics();

        for (ApplicationProgramChange change : applicationProgramChanges) {
            try {
                Map<String, Object> payload = buildApplicationProgramPayload(change);
                String key = Optional.ofNullable(change.getAppCode()).orElseGet(() -> String.valueOf(change.getAppId()));
                sendToTargetTopics(targetProgramTopics, key, payload);

                change.setIsProcessed(1);
                appProgramChangeMapper.updateById(change);
                log.info("已推送应用程序共享增量数据，appCode={}, version={}", change.getAppCode(), change.getCurrentVersion());
            } catch (Exception ex) {
                log.error("推送应用程序共享增量数据失败，appCode={}, version={}", change.getAppCode(), change.getCurrentVersion(), ex);
            }
        }
    }

    @Override
    public void executeDataShareProgramFull() {
        List<ApplicationProgram> applicationPrograms = appProgramMapper.selectList(new LambdaQueryWrapper<>());
        if (applicationPrograms == null || applicationPrograms.isEmpty()) {
            log.info("无待共享的应用程序全量数据");
            return;
        }

        if (!kafkaAvailable()) {
            return;
        }

        List<String> targetProgramTopics = buildTargetProgramTopics();

        String batchId = null;
        for (ApplicationProgram program : applicationPrograms) {
            try {
                if (batchId == null) {
                    batchId = DATE_TIME_FORMATTER.format(java.time.LocalDateTime.now());
                }
                Map<String, Object> payload = buildApplicationProgramPayload(program, batchId);
                String key = Optional.ofNullable(program.getProgramCode()).orElseGet(() -> String.valueOf(program.getApplicationId()));
                sendToTargetTopics(targetProgramTopics, key, payload);
            } catch (Exception ex) {
                log.error("推送应用程序共享全量数据失败，programCode={}", program.getProgramCode(), ex);
            }
        }
    }


    /**
     * 根据SYSTEM_BIZ_DATA_FORMATE模板过滤业务数据，只保留模板中定义的字段
     * 支持下划线格式模板字段与小驼峰格式数据字段的映射
     */
    private Map<String, Object> filterBusinessDataByTemplate(Object businessData) {
        if (businessData == null) {
            return Collections.emptyMap();
        }

        Map<String, Object> filteredData = new LinkedHashMap<>();

        if (businessData instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = (Map<String, Object>) businessData;

            // 遍历模板中定义的字段（下划线格式）
            for (String templateField : SYSTEM_BIZ_DATA_FIELDS) {
                Object value = null;

                // 首先尝试直接匹配下划线格式
                if (dataMap.containsKey(templateField)) {
                    value = dataMap.get(templateField);
                } else {
                    // 使用Hutool转换为小驼峰格式再尝试匹配
                    String camelCaseField = StrUtil.toCamelCase(templateField);
                    if (dataMap.containsKey(camelCaseField)) {
                        value = dataMap.get(camelCaseField);
                    }
                }

                // 如果找到值，使用模板字段名（下划线格式）作为key
                if (value != null) {
                    filteredData.put(templateField, value);
                }
            }
        }

        return filteredData;
    }

    private Map<String, Object> buildAppSystemPayload(InteAppSystemChange change) {
        Map<String, Object> payload = new LinkedHashMap<>();

        Map<String, Object> metadata = new LinkedHashMap<>();
        metadata.put("message_id", UUID.randomUUID().toString().replace("-", ""));
        metadata.put("sync_type", "FULL");
        metadata.put("op_type", toOpType(change.getChangeType()));
        metadata.put("trigger_user", change.getOperator());
        // 增量同步：批次ID为空
        metadata.put("batch_id", null);
        metadata.put("sync_timestamp", change.getChangeTime() == null ? null : DATE_TIME_FORMATTER.format(change.getChangeTime()));
        metadata.put("source_system", "tasp");
        // target_system 将在发送前，按 topic 前缀动态填充
        metadata.put("target_system", null);
        metadata.put("data_type", "app_system");

        Object rawBusinessData;
        try {
            rawBusinessData = JsonUtils.isJson(change.getCurrentData()) ? JsonUtils.parseTree(change.getCurrentData()) : Collections.emptyMap();
        } catch (Exception e) {
            rawBusinessData = Collections.emptyMap();
        }

        // 根据SYSTEM_BIZ_DATA_FORMATE模板过滤业务数据
        Map<String, Object> filteredBusinessData = filterBusinessDataByTemplate(rawBusinessData);

        payload.put("metadata", metadata);
        payload.put("business_data", filteredBusinessData);
        payload.put("dict_data", Collections.emptyList());
        return payload;
    }

    private String toOpType(Integer changeType) {
        if (changeType == null) {
            return "UNKNOWN";
        }
        switch (changeType) {
            case 1:
                return "INSERT";
            case 2:
                return "UPDATE";
            case 3:
                return "DELETE";
            default:
                return "UNKNOWN";
        }
    }

    /**
     * 确保给定 topics 存在：不存在则创建
     */
    private void ensureTopicsExist(Set<String> topics) {
        if (topics == null || topics.isEmpty()) {
            return;
        }
        try (AdminClient admin = buildAdminClient()) {
            if (admin == null) {
                log.warn("Kafka AdminClient 未配置，跳过 topic 检查与创建");
                return;
            }
            Set<String> existing = admin.listTopics().names().get(10, TimeUnit.SECONDS);
            List<NewTopic> toCreate = topics.stream()
                    .filter(t -> !existing.contains(t))
                    .map(t -> new NewTopic(t, kafkaTopicPartitions, kafkaTopicReplicas))
                    .collect(Collectors.toList());
            if (!toCreate.isEmpty()) {
                admin.createTopics(toCreate).all().get(10, TimeUnit.SECONDS);
                log.info("已创建缺失的 Kafka topics: {}", toCreate.stream().map(NewTopic::name).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.error("检查/创建 Kafka topics 失败", e);
        }
    }

    private AdminClient buildAdminClient() {
        try {
            if (kafkaAdmin != null) {
                // Spring 管理的 KafkaAdmin
                return AdminClient.create(kafkaAdmin.getConfigurationProperties());
            }
            if (kafkaBootstrapServers != null && !kafkaBootstrapServers.isEmpty()) {
                Properties props = new Properties();
                props.put("bootstrap.servers", kafkaBootstrapServers);
                return AdminClient.create(props);
            }
        } catch (Exception e) {
            log.error("创建 Kafka AdminClient 失败", e);
        }
        return null;
    }


    private boolean kafkaAvailable() {
        if (kafkaProducerClient == null) {
            log.error("KafkaProducerClient 未注入，无法推送共享数据，请检查 kafka starter 及配置");
            return false;
        }
        return true;
    }

    private List<String> buildTargetSystemTopics() {
        List<String> systemTopics = new ArrayList<>();
        R<List<DictItem>> syncNextSystem = dictClient.getDictItemList("sync_next_system");
        if (syncNextSystem != null && syncNextSystem.getData() != null && !syncNextSystem.getData().isEmpty()) {
            List<DictItem> dictItems = syncNextSystem.getData();
            dictItems.stream()
                    .map(DictItem::getValue)
                    .filter(value -> !dataShareTopicExclusive.contains(value))
                    .forEach(value -> systemTopics.add(value + ".application_system.v1"));

            ensureTopicsExist(new HashSet<>(systemTopics));
        }
        return systemTopics;
    }

    private List<String> buildTargetProgramTopics() {
        List<String> programTopics = new ArrayList<>();
        R<List<DictItem>> syncNextSystem = dictClient.getDictItemList("sync_next_system");
        if (syncNextSystem != null && syncNextSystem.getData() != null && !syncNextSystem.getData().isEmpty()) {
            List<DictItem> dictItems = syncNextSystem.getData();
            dictItems.stream()
                    .map(DictItem::getValue)
                    .filter(value -> !dataShareTopicExclusive.contains(value))
                    .forEach(value -> programTopics.add(value + ".application.v1"));

            ensureTopicsExist(new HashSet<>(programTopics));
        }
        return programTopics;
    }

    private void sendToTargetTopics(List<String> targetSystemTopics, String key, Map<String, Object> basePayload) {
        // 检查business_data是否为空
        Object businessData = basePayload.get("business_data");
        if (businessData == null || (businessData instanceof Map && ((Map<?, ?>) businessData).isEmpty())) {
            log.info("business_data为空，跳过推送消息到kafka，key={}", key);
            return;
        }

        for (String topic : targetSystemTopics) {
            try {
                Map<String, Object> payload = new LinkedHashMap<>(basePayload);
                Object metaObj = payload.get("metadata");
                if (metaObj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> metadata = new LinkedHashMap<>((Map<String, Object>) metaObj);
                    String targetSystem = topic;
                    int idx = topic.indexOf('.');
                    if (idx > 0) {
                        targetSystem = topic.substring(0, idx);
                    }
                    metadata.put("target_system", targetSystem);
                    payload.put("metadata", metadata);
                }
                String message = JsonUtils.toJsonString(payload);
                kafkaProducerClient.send(topic, key, message);
            } catch (Exception sendEx) {
                log.error("发送到目标 topic 失败, topic={}, key={}", topic, key, sendEx);
            }
        }
    }

    private Map<String, Object> buildAppSystemPayload(AppSystem appSystem, String batchId) {
        Map<String, Object> payload = new LinkedHashMap<>();

        Map<String, Object> metadata = new LinkedHashMap<>();
        metadata.put("message_id", UUID.randomUUID().toString().replace("-", ""));
        metadata.put("sync_type", "FULL");
        metadata.put("op_type", "INSERT");
        metadata.put("trigger_user", "system");
        // 全量同步：批次ID为第一条数据推送时间戳
        metadata.put("batch_id", batchId);
        metadata.put("sync_timestamp", DATE_TIME_FORMATTER.format(java.time.LocalDateTime.now()));
        metadata.put("source_system", "tasp");
        // target_system 将在发送前，按 topic 前缀动态填充
        metadata.put("target_system", null);
        metadata.put("data_type", "app_system");

        Object rawBusinessData;
        try {
            rawBusinessData = JsonUtils.parseTree(JsonUtils.toJsonString(appSystem));
        } catch (Exception e) {
            rawBusinessData = Collections.emptyMap();
        }

        // 根据SYSTEM_BIZ_DATA_FORMATE模板过滤业务数据
        Map<String, Object> filteredBusinessData = filterBusinessDataByTemplate(rawBusinessData);

        payload.put("metadata", metadata);
        payload.put("business_data", filteredBusinessData);
        payload.put("dict_data", Collections.emptyList());
        return payload;
    }

    /**
     * 构建BATCH类型的应用系统payload
     *
     * @param appSystem   应用系统数据
     * @param batchId     批次ID
     * @param triggerUser 触发用户
     * @return payload
     */
    private Map<String, Object> buildBatchAppSystemPayload(AppSystem appSystem, String batchId, String triggerUser) {
        Map<String, Object> payload = new LinkedHashMap<>();

        Map<String, Object> metadata = new LinkedHashMap<>();
        metadata.put("message_id", UUID.randomUUID().toString().replace("-", ""));
        metadata.put("sync_type", "BATCH");
        metadata.put("op_type", "INSERT");
        metadata.put("trigger_user", triggerUser);
        // BATCH同步：批次ID为手动推送时的时间戳
        metadata.put("batch_id", batchId);
        metadata.put("sync_timestamp", DATE_TIME_FORMATTER.format(java.time.LocalDateTime.now()));
        metadata.put("source_system", "tasp");
        // target_system 将在发送前，按 topic 前缀动态填充
        metadata.put("target_system", null);
        metadata.put("data_type", "app_system");

        Object rawBusinessData;
        try {
            rawBusinessData = JsonUtils.parseTree(JsonUtils.toJsonString(appSystem));
        } catch (Exception e) {
            rawBusinessData = Collections.emptyMap();
        }

        // 根据SYSTEM_BIZ_DATA_FORMATE模板过滤业务数据
        Map<String, Object> filteredBusinessData = filterBusinessDataByTemplate(rawBusinessData);

        payload.put("metadata", metadata);
        payload.put("business_data", filteredBusinessData);
        payload.put("dict_data", Collections.emptyList());
        return payload;
    }

    private Map<String, Object> buildApplicationProgramPayload(ApplicationProgramChange change) {
        Map<String, Object> payload = new LinkedHashMap<>();

        Map<String, Object> metadata = new LinkedHashMap<>();
        metadata.put("message_id", UUID.randomUUID().toString().replace("-", ""));
        metadata.put("sync_type", "FULL");
        metadata.put("op_type", toOpType(change.getChangeType()));
        metadata.put("trigger_user", change.getOperator());
        // 增量同步：批次ID为空
        metadata.put("batch_id", null);
        metadata.put("sync_timestamp", change.getChangeTime() == null ? null : DATE_TIME_FORMATTER.format(change.getChangeTime()));
        metadata.put("source_system", "tasp");
        // target_system 将在发送前，按 topic 前缀动态填充
        metadata.put("target_system", null);
        metadata.put("data_type", "application");

        Object businessData;
        try {
            businessData = JsonUtils.isJson(change.getCurrentData()) ? JsonUtils.parseTree(change.getCurrentData()) : Collections.emptyMap();
        } catch (Exception e) {
            businessData = Collections.emptyMap();
        }

        payload.put("metadata", metadata);
        payload.put("business_data", businessData);
        payload.put("dict_data", Collections.emptyList());
        return payload;
    }

    private Map<String, Object> buildApplicationProgramPayload(ApplicationProgram program, String batchId) {
        Map<String, Object> payload = new LinkedHashMap<>();

        Map<String, Object> metadata = new LinkedHashMap<>();
        metadata.put("message_id", UUID.randomUUID().toString().replace("-", ""));
        metadata.put("sync_type", "FULL");
        metadata.put("op_type", "INSERT");
        metadata.put("trigger_user", "system");
        // 全量同步：批次ID为第一条数据推送时间戳
        metadata.put("batch_id", batchId);
        metadata.put("sync_timestamp", DATE_TIME_FORMATTER.format(java.time.LocalDateTime.now()));
        metadata.put("source_system", "tasp");
        // target_system 将在发送前，按 topic 前缀动态填充
        metadata.put("target_system", null);
        metadata.put("data_type", "application");

        Object businessData;
        try {
            businessData = JsonUtils.parseTree(JsonUtils.toJsonString(program));
        } catch (Exception e) {
            businessData = Collections.emptyMap();
        }

        payload.put("metadata", metadata);
        payload.put("business_data", businessData);
        payload.put("dict_data", Collections.emptyList());
        return payload;
    }

    /**
     * 记录应用系统推送日志
     *
     * @param appSystem   应用系统
     * @param triggerUser 触发用户
     * @param syncType    同步类型
     * @param syncStatus  同步状态
     */
    private void recordAppSyncLog(AppSystem appSystem, String triggerUser, String syncType, String syncStatus) {
        try {
            AppSyncLog appSyncLog = new AppSyncLog();
            appSyncLog.setAppId(appSystem.getId());
            appSyncLog.setAppName(appSystem.getCnName());
            appSyncLog.setDownStreamName("kafka");
            // 对于手动推送，创建者ID和名称都使用触发用户
            // 这里假设triggerUser是用户名，ID设置为null或者使用默认值
            appSyncLog.setCreatorId(null); // 手动推送时可能没有用户ID
            appSyncLog.setCreatorName(triggerUser);
            appSyncLog.setCreatedTime(java.time.LocalDateTime.now());
            appSyncLog.setSyncStatus(syncStatus);

            appSyncLogMapper.insert(appSyncLog);
            log.info("记录应用系统推送日志成功，appId={}, syncType={}, syncStatus={}", appSystem.getId(), syncType, syncStatus);
        } catch (Exception e) {
            log.error("记录应用系统推送日志失败，appId={}", appSystem.getId(), e);
        }
    }

    /**
     * 手动推送单个应用系统到kafka
     *
     * @param appSystem   应用系统数据
     * @param triggerUser 触发用户
     * @return 是否推送成功
     */
    @Override
    public boolean pushAppSystemToKafka(AppSystem appSystem, String triggerUser) {
        if (appSystem == null) {
            log.error("推送应用系统到kafka失败，应用系统数据为空");
            return false;
        }

        if (!kafkaAvailable()) {
            log.error("推送应用系统到kafka失败，kafka服务不可用");
            return false;
        }

        try {
            // 计算目标系统的专属 topic 列表并确保存在
            List<String> targetSystemTopics = buildTargetSystemTopics();

            // 生成批次ID
            String batchId = DATE_TIME_FORMATTER.format(java.time.LocalDateTime.now());

            // 构建payload
            Map<String, Object> payload = buildBatchAppSystemPayload(appSystem, batchId, triggerUser);

            // 发送到各系统的专属 topic（按 topic 前缀设置 target_system）
            sendToTargetTopics(targetSystemTopics, appSystem.getCode(), payload);

            // 推送成功后新增执行记录
            recordAppSyncLog(appSystem, triggerUser, "BATCH", "成功");

            log.info("手动推送应用系统到kafka成功，systemCode={}, batchId={}", appSystem.getCode(), batchId);
            return true;

        } catch (Exception ex) {
            log.error("手动推送应用系统到kafka失败，systemCode={}", appSystem.getCode(), ex);

            // 推送失败时也记录日志
            try {
                recordAppSyncLog(appSystem, triggerUser, "BATCH", "失败");
            } catch (Exception logEx) {
                log.error("记录应用系统推送日志失败", logEx);
            }

            return false;
        }
    }
}
