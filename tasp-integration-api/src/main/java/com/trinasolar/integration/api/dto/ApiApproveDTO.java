package com.trinasolar.integration.api.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ApiApproveDTO implements Serializable {
    private String processKey;
    private String businessType;
    private String instanceName;
    private String businessData;
    private String sourceName;
    // 应用系统id 后续使用
    private String businessId;
    // 拓展字段
    private String extendFields;

    @Data
    private static class ExtendFields {
        private String apiId;
        private Long pubOrgId;
    }

    /**
     * 订阅流程
     *
     * @param apiName
     * @param businessData
     * @param subOrgId
     * @return
     */
    public static ApiApproveDTO subInit(String apiName, String businessData, String subOrgId) {
        ApiApproveDTO apiApproveDTO = new ApiApproveDTO();
        apiApproveDTO.processKey = "api_order";
        apiApproveDTO.businessType = "api_order";
        apiApproveDTO.instanceName = apiName;
        apiApproveDTO.sourceName = "API市场订阅审批";
        apiApproveDTO.businessData = businessData;
        ExtendFields extendFields = new ExtendFields();
        extendFields.setApiId(subOrgId);
        apiApproveDTO.extendFields = JSONObject.toJSONString(extendFields);
        return apiApproveDTO;
    }

    /**
     * 发布流程
     *
     * @param apiName
     * @param businessData
     * @param pubOrgId
     * @return
     */
    public static ApiApproveDTO pubInit(String apiName, String businessData, Long pubOrgId) {
        ApiApproveDTO apiApproveDTO = new ApiApproveDTO();
        apiApproveDTO.processKey = "api_pub_order";
        apiApproveDTO.businessType = "api_pub_order";
        apiApproveDTO.instanceName = apiName;
        apiApproveDTO.sourceName = "API市场发布审批";
        apiApproveDTO.businessData = businessData;
        ExtendFields extendFields = new ExtendFields();
        extendFields.setPubOrgId(pubOrgId);
        apiApproveDTO.extendFields = JSONObject.toJSONString(extendFields);
        return apiApproveDTO;
    }

}
