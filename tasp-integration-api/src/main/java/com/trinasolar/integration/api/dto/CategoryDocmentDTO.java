package com.trinasolar.integration.api.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName CategoriesDTO
 * @Description
 * <AUTHOR>
 * @Date 2025/5/15 10:18
 **/
@Data
public class CategoryDocmentDTO implements Cloneable {

   private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
   private Long id;

    /**
     * 文档id
     */
   private Long documentId;

    /**
     * 文档空间id
     */
    private Long spaceId;

    /**
     * 分类id
     */
   private Long categoriesId;

    /**
     * 目录id
     */
   private String categoriesIds;

    /**
     * 标签
     */
   private List<String> tagIds;

    /**
     *  排序
     */
   private Integer sort;

   private String docVersion;

   private Integer isRelease=0;

   private Integer isRag;


   private Integer isApprove=1;

   private String alias;

   private String keyWords;

   private String description;

   /**
    * 实现对象深度克隆
    * @return 克隆后的对象
    */
   @Override
   public CategoryDocmentDTO clone() {
       try {
           // 调用父类的clone方法进行浅克隆
           CategoryDocmentDTO cloned = (CategoryDocmentDTO) super.clone();

           // 对引用类型字段进行深度克隆
           if (this.tagIds != null) {
               cloned.tagIds = new ArrayList<>(this.tagIds);
           }

           return cloned;
       } catch (CloneNotSupportedException e) {
           // 如果克隆失败，抛出运行时异常
           throw new RuntimeException("克隆对象失败", e);
       }
   }

   /**
    * 创建当前对象的深度副本（便捷方法）
    * @return 深度克隆的对象
    */
   public CategoryDocmentDTO deepCopy() {
       return this.clone();
   }
}
